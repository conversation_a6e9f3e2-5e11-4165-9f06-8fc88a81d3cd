import globals from 'globals';
import pluginVue from 'eslint-plugin-vue';
import pluginImport from 'eslint-plugin-import';
import { defineConfig } from 'eslint/config';

export default defineConfig([
  {
    files: ['**/*.{js,mjs,cjs,vue}'],
    languageOptions: { globals: globals.browser },
    plugins: { import: pluginImport },
    ignores: [
      'dist/',
      'src-capacitor/',
      'src-cordova/',
      '.quasar/',
      'node_modules/',
      '.eslintrc.js',
    ],
    rules: {
      'arrow-body-style': ['error', 'as-needed'],
      'arrow-parens': ['error', 'as-needed'],
      'camelcase': 'off',
      'comma-dangle': ['error', {
        arrays: 'always-multiline',
        objects: 'always-multiline',
        imports: 'always-multiline',
        exports: 'always-multiline',
        functions: 'never',
      }],
      'consistent-return': 'error',
      'curly': ['error', 'all'],
      'eol-last': ['error', 'always'],
      'eqeqeq': ['error', 'always'],
      'func-names': ['warn', 'as-needed'],
      'function-paren-newline': ['error', 'consistent'],
      'indent': 'off',
      'key-spacing': ['error', { beforeColon: false, afterColon: true }],
      'keyword-spacing': ['error', { before: true, after: true }],
      'max-len': ['error', 150],
      'new-cap': ['error', { newIsCap: true, capIsNew: false }],
      'no-console': 'warn',
      'no-continue': 'error',
      'no-lonely-if': 'error',
      'no-mixed-operators': 'off',
      'no-multi-assign': ['error'],
      'no-multiple-empty-lines': ['error', { max: 1, maxEOF: 0 }],
      'no-restricted-syntax': ['error', 'ForInStatement', 'LabeledStatement', 'WithStatement'],
      'no-shadow': ['error'],
      'no-trailing-spaces': ['error'],
      'no-underscore-dangle': 'off',
      'object-curly-newline': ['error', { consistent: true }],
      'object-property-newline': ['error', { allowMultiplePropertiesPerLine: true }],
      'prefer-const': ['error'],
      'quotes': ['error', 'single', { avoidEscape: true }],
      'semi': ['error', 'always'],
      'space-before-function-paren': 'off',
      'no-param-reassign': 'off',
      'no-void': 'off',
      'no-nested-ternary': 'off',
      'max-classes-per-file': 'off',
      'no-plusplus': 'off',
      'class-methods-use-this': 'off',
      // Các rule của plugin import (Airbnb dùng plugin này)
      'import/no-absolute-path': 'error',
      'import/no-dynamic-require': 'error',
      'import/no-self-import': 'error',
      'import/no-cycle': 'error',
      'import/no-useless-path-segments': 'error',
      'import/order': ['error', {
        groups: [['builtin', 'external', 'internal']],
        'newlines-between': 'always',
      }],
      'import/newline-after-import': 'off',
      'import/no-mutable-exports': 'error',
      'import/first': 'off',
      'import/named': 'error',
      'import/namespace': 'error',
      'import/default': 'error',
      'import/export': 'error',
      'import/extensions': 'off',
      'import/no-unresolved': 'off',
      'import/no-extraneous-dependencies': 'off',
      'import/prefer-default-export': 'off',
    },
  },
  pluginVue.configs['flat/essential'],
]);
