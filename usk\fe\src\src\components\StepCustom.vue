<template>
  <q-tabs
    active-class="tw:!bg-[#004AB9] tw:text-white"
    align="justify"
    content-class="tw:gap-6 tw:grid tw:grid-cols-2"
    class="tw:text-[#004AB9]"
    v-model="model"
    @update:model-value="handleChangeStep"
  >
    <q-tab
      name="1"
      class="tw:cursor-default tw:justify-start tw:items-center tw:rounded-[8px] tw:min-h-[5rem] tw:h-[5rem] tw:xl:min-h-[3.5rem] tw:xl:h-[3.5rem]"
      :class="{
        'tw:bg-[#004AB9] tw:text-white': model === STEP_ENUM.STEP_1,
        'tw:bg-[#E3F3FF] tw:border-2 tw:xl:border-3 tw:border-[#004AB9]': model !== STEP_ENUM.STEP_1,
      }"
    >
      <div
        class="tw:flex tw:items-center tw:gap-2"
        :class="{
          'tw:transform tw:-translate-y-1': model > STEP_ENUM.STEP_1,
        }"
      >
        <span class="tw:text-xs-design">ステップ1</span>
        <q-icon
          v-if="model > STEP_ENUM.STEP_1"
          name="check_circle"
          class="tw:fill-white tw:xl:pt-1"
          size="2rem"
        />
      </div>
    </q-tab>
    <q-tab
      name="2"
      class="tw:cursor-default tw:justify-start tw:rounded-[8px] tw:min-h-[5rem] tw:h-[5rem] tw:xl:min-h-[3.5rem] tw:xl:h-[3.5rem]"
      :class="{
        'tw:bg-[#004AB9] tw:text-white': model === STEP_ENUM.STEP_2,
        'tw:bg-[#CACACA] tw:text-[#333333]': model !== STEP_ENUM.STEP_2,
      }"
    >
      <span class="tw:text-xs-design">ステップ2</span>
    </q-tab>
  </q-tabs>
</template>
<script setup>
import { STEP_ENUM } from 'src/helpers/constants';

const model = defineModel();

// Handle step change
// Don't allow direct model manipulation
const handleChangeStep = value => {
  if (value === STEP_ENUM.STEP_1) {
    model.value = STEP_ENUM.STEP_2;
  } else if (value === STEP_ENUM.STEP_2) {
    model.value = STEP_ENUM.STEP_1;
  }
};
</script>
<style scoped>
:deep(.q-tab__indicator) {
  display: none;
}

:deep(.q-tab) {
  background-color: #cacaca;
}
</style>
