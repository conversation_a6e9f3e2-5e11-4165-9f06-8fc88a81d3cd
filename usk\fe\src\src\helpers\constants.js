const ROUTER_COMMON = [
  'auth',
  'common',
  'users',
  'setting',
  'notification',
  'staff',
  'profile',
  'located-transaction',
  'admin/user',
  'admin/setting',
  'notification-manager',
  'regions',
];
const SERVICE_ID = '756E616769';

const INPUT_VOLUME_SHIPMENT_TYPE_OPTIONS = [
  { label: '重量', value: '1' },
  { label: '尾数', value: '2' },
];

const INVENTORY_STATES_ENUM = {
  EMPTY: 'empty',
  NOT_EMPTY: 'not_empty',
  ALL: 'all',
};

const ROLES_ENUM = {
  SYSTEM_ADMIN: '99',
  ADMIN: '90',
  LOCAL_GOVERNMENT_ADMIN: '91',
  FISHERIES_DEPARTMENT_ADMIN: '92',
  NORMAL_USER: '00',
};

const ROLES_ENUM_OPTIONS_VALUES = {
  CATCH_STAFF: '01',
  CATCH_ENTERPRISE: '02',
  DISTRIBUTE_ENTERPRISE: '03',
  EEL_FARMING_ENTERPRISE: '04',
  DISTRIBUTE_STAFF: '05',
};

const USER_ROLE_OPTIONS = [
  { label: '採捕従事者', value: ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF },
  { label: '採捕事業者', value: ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE },
  {
    label: '取扱事業者',
    value: ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_ENTERPRISE,
  },
  { label: '取扱従事者', value: ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_STAFF },
  {
    label: '養鰻事業者',
    value: ROLES_ENUM_OPTIONS_VALUES.EEL_FARMING_ENTERPRISE,
  },
];

const USER_ROLE_NOTIFY_OPTIONS = [
  { label: '採捕従事者', value: ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF },
  { label: '採捕事業者', value: ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE },
  {
    label: '取扱事業者',
    value: ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_ENTERPRISE,
  },
  { label: '取扱従事者', value: ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_STAFF },
];

const ENTERPRISE_TYPE_ENUM = {
  CATCH_ENTERPRISE: 0,
  DISTRIBUTE_ENTERPRISE: 5,
  EEL_FARMING_ENTERPRISE: 9,
  FARM: 98,
  FOREIGN: 99,
};

const STAFF_TYPE_ENUM = {
  ENTERPRISE: 0,
  STAFF: 1,
};

const ENTERPRISE_TYPE_OPTIONS = [
  { label: '採捕事業者', value: ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE },
  { label: '取扱事業者', value: ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE },
  { label: '養鰻事業者', value: ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE },
];

const STAFF_TYPE_OPTIONS = [
  { label: '事業者', value: STAFF_TYPE_ENUM.ENTERPRISE },
  { label: '従事者', value: STAFF_TYPE_ENUM.STAFF },
];

const SORT_BY_INVENTORY_ENUM = {
  CREATED_ON: 'created_on',
  ARRIVAL_DATE: 'latest_arrival_date',
  WEIGHT_INVENTORY: 'net_weight_inventory',
  WEIGHT_TOTAL: 'net_weight_total',
  GROUP: 'group_name',
  NEW_WEIGHT_INVENTORY: 'new_net_weight_inventory',
};

// TODO: Remove this after refactoring
const UNIT_TYPE_SETTING_ENUM = {
  WEIGHT_ONLY: 1,
  QUANTITY_ONLY: 2,
};

const VOLUME_TYPE_ENUM = {
  WEIGHT: 1,
  QUANTITY: 2,
};

const SHOW_SHIPPING_DESTINATION_ENUM = {
  SHOW: true,
  HIDDEN: false,
};

const EXPORT_ENUM = {
  SHOW: true,
  HIDDEN: false,
};

const SHOW_DEFAULT_SCAN_QR_ENUM = {
  USE_CAMERA: 1,
  USE_SCAN: 2,
  USE_USER_ID: 3,
};

const OPTION_TYPE_ENUM = {
  USER_SUPPLIER: 'user-supplier',
  USER_SHIPPER: 'user-shipper',
  GROUP: 'group',
};

const TYPE_DIFFERENCE_WEIGHT_ENUM = {
  DEATH: 1,
  WEIGHT_ERROR: 2,
  OTHER: 3,
};

const PARTNER_TYPE_ENUM = {
  SUPPLIER: 1,
  SHIPPER: 2,
};

const SHIPPING_TYPE_ENUM = {
  ARRIVAL_MANUAL: 0,
  PROXY: 1,
  NORMAL: 2,
};

const NOTIFICATION_STATUS_ENUM = {
  ALL: 'all',
  READ: 'read',
  UNREAD: 'unread',
};

const ADMIN_SYSTEM_SETTING_KEYS_ENUM = {
  WEIGHT_ALERT_THRESHOLD: 'weight_alert_threshold',
  EDIT_DEADLINE_FOR_ARRIVAL: 'edit_deadline_for_arrival_shipping',
  CATCHING_RESERVE_PERIOD: 'catching_reserve_period',
};

const USER_STATUS_ENUM = {
  ACTIVE: 0,
  NONACTIVE: 1,
  PENDING: 2,
};

const RECEIPT_NUMBER_VALUE_ENUM = {
  ONE: 1,
  TWO: 2,
  THREE: 3,
  FOUR: 4,
  FIVE: 5,
};

const REPORT_TYPE_ENUM = {
  PDF_FILE: 1,
  PRINTED_RECEIPT: 2,
};

const LIST_PROVINCE = [
  {
    value: 0,
    label: '',
  },
  {
    value: 1,
    label: '愛知県',
  },
  {
    value: 2,
    label: '鹿児島県',
  },
  {
    value: 3,
    label: '宮崎県',
  },
  {
    value: 4,
    label: '高知県',
  },
  {
    value: 5,
    label: '三重県',
  },
];

const LIST_PROVINCE_SEARCH = [
  {
    value: '',
    label: '',
  },
  {
    value: 0,
    label: 'なし',
  },
  {
    value: 1,
    label: '愛知県',
  },
  {
    value: 2,
    label: '鹿児島県',
  },
  {
    value: 3,
    label: '宮崎県',
  },
  {
    value: 4,
    label: '高知県',
  },
  {
    value: 5,
    label: '三重県',
  },
];

const PROVINCES_ENUM = {
  NONE: 0,
  AICHI: 1,
  KAGOSHIMA: 2,
  MIYAZAKI: 3,
  KOCHI: 4,
  MIE: 5,
};

const OPTIONS_STATUS_USER = [
  {
    value: 0,
    label: '利用中',
  },
  {
    value: 1,
    label: '無許可',
  },
  {
    value: 2,
    label: '非表示',
  },
];

const OPTIONS_STATUS_USER_REGISTER = [
  {
    value: 0,
    label: '利用中',
  },
  {
    value: 1,
    label: '無許可',
  },
];

const OPTIONS_INVENTORY_CONTROL_TYPE = [
  {
    value: 1,
    label: '事業者が集約して管理',
  },
  {
    value: 2,
    label: '各従事者が個別管理',
  },
];

const INCLUDE_TAX_TYPE_OPTIONS = [
  {
    value: 1,
    label: '内税',
  },
  {
    value: 2,
    label: '外税',
  },
];

const STAFF_OR_ENTERPRISE = [
  { value: 0, label: '事業者' },
  { value: 1, label: '従事者' },
];

const REPORT_TYPE_OPTIONS = [
  { value: REPORT_TYPE_ENUM.PDF_FILE, label: 'PDF' },
  {
    value: REPORT_TYPE_ENUM.PRINTED_RECEIPT,
    label: 'レシートプリンターから出力',
  },
];

const BUSINESS_TYPE_OPTIONS = [
  { value: 1, label: '採捕事業者' },
  { value: 0, label: '取扱事業者' },
];
const USER_STATUS_REGISTER_ENUM = {
  TEMPORARY: 0,
  OFFICIAL: 1,
};

const OPTIONS_STATUS_REGISTER_ACCOUNT = [
  {
    value: 0,
    label: '仮登録',
  },
  {
    value: 1,
    label: '本登録',
  },
];

const OPTION_ENABLE_EXPORT_FUNCTION = [
  {
    value: true,
    label: '有効',
  },
  {
    value: false,
    label: '無効',
  },
];

const ROUTER_NESTED_LIST = {
  employeeList: {
    nameOfLocal: 'employeeList',
    routers: ['employeeDetail', 'employeeList'],
  },
  partner: {
    nameOfLocal: 'partner',
    routers: [
      'partnerHybrid',
      'partnerHybridDetail',
      'partnerHybridDetailConfirm',
      'partnerInput',
      'partnerConfirm',
      'partnerDetail',
    ],
  },
  inventoryList: {
    nameOfLocal: 'inventoryList',
    routers: [
      'inventoryList',
      'inventoryDetail',
      'inventoryEditedList',
      'inventoryEditedDetail',
    ],
  },
  inventoryEditedList: {
    nameOfLocal: 'inventoryEditedList',
    routers: [
      'inventoryEditedList',
      'inventoryEditedDetail',
    ],
  },
};

const TYPE_STAFF = {
  0: '事業者',
  1: '従事者区分',
};

const STEP_ENUM = {
  STEP_1: '1',
  STEP_2: '2',
};

const STATUS_USER_ENUM = {
  ACTIVE: 0,
  NONACTIVE: 1,
  PENDING: 2,
};

const INVENTORY_TYPE_ENUM = {
  DOMESTIC: 0,
  IMPORTED_EXPORTED: 1,
  OTHER: 2,
};

export {
  INPUT_VOLUME_SHIPMENT_TYPE_OPTIONS,
  ROUTER_COMMON,
  SERVICE_ID,
  ROLES_ENUM,
  ENTERPRISE_TYPE_ENUM,
  UNIT_TYPE_SETTING_ENUM,
  SHOW_SHIPPING_DESTINATION_ENUM,
  SHOW_DEFAULT_SCAN_QR_ENUM,
  TYPE_DIFFERENCE_WEIGHT_ENUM,
  OPTION_TYPE_ENUM,
  SORT_BY_INVENTORY_ENUM,
  PARTNER_TYPE_ENUM,
  EXPORT_ENUM,
  NOTIFICATION_STATUS_ENUM,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
  INVENTORY_STATES_ENUM,
  LIST_PROVINCE,
  OPTIONS_STATUS_USER,
  PROVINCES_ENUM,
  STAFF_OR_ENTERPRISE,
  USER_STATUS_ENUM,
  USER_ROLE_OPTIONS,
  LIST_PROVINCE_SEARCH,
  SHIPPING_TYPE_ENUM,
  REPORT_TYPE_OPTIONS,
  RECEIPT_NUMBER_VALUE_ENUM,
  REPORT_TYPE_ENUM,
  OPTIONS_INVENTORY_CONTROL_TYPE,
  INCLUDE_TAX_TYPE_OPTIONS,
  BUSINESS_TYPE_OPTIONS,
  USER_STATUS_REGISTER_ENUM,
  STAFF_TYPE_ENUM,
  ROLES_ENUM_OPTIONS_VALUES,
  ENTERPRISE_TYPE_OPTIONS,
  OPTIONS_STATUS_REGISTER_ACCOUNT,
  OPTIONS_STATUS_USER_REGISTER,
  STAFF_TYPE_OPTIONS,
  USER_ROLE_NOTIFY_OPTIONS,
  OPTION_ENABLE_EXPORT_FUNCTION,
  ROUTER_NESTED_LIST,
  TYPE_STAFF,
  STEP_ENUM,
  STATUS_USER_ENUM,
  VOLUME_TYPE_ENUM,
  INVENTORY_TYPE_ENUM,
};
