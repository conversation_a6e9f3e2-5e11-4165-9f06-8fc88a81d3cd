<template>
  <div class="tw:flex tw:flex-col tw:h-full tw:pb-[14rem] tw:tl:pb-[8rem]">
    <StepCustom v-model="step" class="tw:pb-3" />
    <q-tab-panels v-model="step" animated class="tw:text-[#333333] tw:flex-1">
      <q-tab-panel :name="STEP_ENUM.STEP_1" class="tw:p-4">
        <h2 :class="`tw:text-xs-design tw:tl:font-bold tw:my-3 tw:tl:mt-0`">
          必要項目を入力して「次に進む」ボタンを押してください。
        </h2>
        <form
          class="tw:grid tw:grid-cols-1 tw:gap-y-10 tw:tl:grid-cols-2 tw:gap-x-12"
        >
          <!-- 漁獲/荷口番号 -->
          <div>
            <BaseLabel label="漁獲/荷口番号" isRequired />
            <div
              class="tw:flex tw:mb-2 tw:text-xl-design tw:gap-2 tw:text-[#333333] tw:items-start"
            >
              <div class="tw:shrink-0">{{ maskMainCode(form.mainCode) }}</div>
              <BaseInput
                v-model="form.subCode"
                autocomplete="nope"
                maxlength="3"
                inputmode="numeric"
                outlined
                input-class="tw:text-xl-design tw:text-[#333333]"
                class="tw:w-full vs tw:mt-0.5"
                :error="!!errorStep1.subCode"
                :error-message="errorStep1.subCode"
                :mask="{
                  mask: /^\d{0,3}$/,
                }"
              >
              </BaseInput>
            </div>
          </div>
          <!-- 仕入先 -->
          <div>
            <BaseLabel label="仕入先" isRequired />
            <div>
              <BasePartnerSelect
                :error-message="errorStep1.supplier"
                :error="!!errorStep1.supplier"
                v-model="form.supplier"
                @update:model-value="handleSelectPartner"
              />
            </div>
          </div>
          <!-- 入荷日 -->
          <div>
            <BaseLabel label="入荷日" isRequired />
            <BaseDatePicker
              v-model="form.date"
              class="tw:flex-1"
              input-class="tw:text-xl-design tw:text-[#333333]"
              :class="{
                'tw:sm:mb-[0.5rem] tw:tl:mb-[1.3rem]': !!errorStep1.date,
              }"
              :error="!!errorStep1.date"
              :error-message="errorStep1.date"
            />
          </div>
          <!-- 入荷登録単位 -->
          <div>
            <BaseLabel label="入荷登録単位" />
            <q-radio
              v-for="(item, index) in INPUT_VOLUME_TYPE_OPTIONS"
              v-model="form.volumeType"
              :key="index"
              :val="item.value"
              size="5rem"
              class="tw:transform tw:-translate-x-5"
              @click="handleChangeVolumeType(item.value)"
            >
              <span class="tw:text-xl-design tw:text-[#333333]">
                {{ item.label }}
              </span>
            </q-radio>
          </div>
        </form>
      </q-tab-panel>
      <q-tab-panel :name="STEP_ENUM.STEP_2" class="tw:p-4">
        <h2 :class="`tw:text-xs-design tw:tl:font-bold tw:my-3 tw:tl:mt-0`">
          必要項目を入力して「次に進む」ボタンを押してください。
        </h2>
        <form
          class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:gap-x-12 tw:gap-y-2"
        >
          <div
            v-if="form.volumeType === VOLUME_TYPE_ENUM.WEIGHT"
            class="tw:mb-8 tw:tl:mb-0"
          >
            <!-- 全体重量 -->
            <BaseLabel label="全体重量" isRequired />
            <BaseInput
              autofocus
              clearable
              type="text"
              maxlength="9"
              inputmode="numeric"
              autocomplete="nope"
              :model-value="form.grossWeight"
              @update:model-value="handleInputGrossWeight"
              :mask="{
                mask: Number,
                min: 0,
                thousandsSeparator: ',',
                scale: 2,
                radix: '.',
                autofix: true,
                lazy: false,
              }"
              outlined
              class="tw:!h-[7rem]"
              input-class="tw:text-right tw:text-xl-design"
              :error="!!errorStep2.grossWeight"
              :error-message="errorStep2.grossWeight"
            >
              <template v-slot:append>
                <div
                  :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-m-design tw:mt-2`"
                >
                  g
                </div>
              </template>
            </BaseInput>
          </div>
          <div
            v-if="form.volumeType === VOLUME_TYPE_ENUM.WEIGHT"
            class="tw:flex tw:items-end tw:relative tw:mb-8 tw:tl:mb-0"
          >
            <!-- subtraction sign sm -->
            <div
              class="tw:h-1 tw:bg-[#7E8093] tw:px-4 tw:mb-8 tw:mx-5 tw:tl:hidden"
            />
            <div class="tw:flex-1">
              <!-- subtraction sign tl -->
              <div
                class="tw:h-1 tw:bg-[#7E8093] tw:px-3 tw:mb-8 tw:mx-5 tw:hidden tw:tl:block tw:absolute tw:top-[4.8rem] tw:-left-[3.5rem]"
              />
              <!-- 風袋 -->
              <BaseLabel label="風袋" isRequired />
              <BaseInput
                clearable
                type="text"
                maxlength="10"
                inputmode="numeric"
                autocomplete="nope"
                :model-value="form.tareWeight"
                @update:model-value="handleInputTareWeight"
                :mask="{
                  mask: Number,
                  min: 0,
                  thousandsSeparator: ',',
                  scale: 2,
                  radix: '.',
                  autofix: true,
                  lazy: false,
                }"
                outlined
                input-class="tw:text-right tw:text-xl-design"
                :error="!!errorStep2.tareWeight && !errorStep2.grossWeight"
                :error-message="errorStep2.tareWeight"
              >
                <template v-slot:append>
                  <div
                    :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-m-design tw:mt-2`"
                  >
                    g
                  </div>
                </template>
              </BaseInput>
            </div>
          </div>
          <!-- Divider -->
          <div
            v-if="form.volumeType === VOLUME_TYPE_ENUM.WEIGHT"
            class="tw:tl:col-span-2 tw:h-[1px] tw:bg-[#CBCBCB] tw:mt-2"
            :class="{
              'tw:tl:mt-14': errorStep2.tareWeight || errorStep2.grossWeight,
              'tw:mt-14': errorStep2.tareWeight,
            }"
          />
          <div
            v-if="form.volumeType === VOLUME_TYPE_ENUM.WEIGHT"
            class="tw:tl:col-span-2 tw:flex tw:justify-between tw:items-center tw:gap-4"
          >
            <!-- 入荷量 -->
            <BaseLabel label="入荷量" />
            <div class="tw:font-bold">
              <span class="tw:text-xl-design">
                {{ form.netWeight }}
              </span>
              <span class="tw:text-m-design"> g </span>
            </div>
          </div>
          <div
            v-if="form.volumeType === VOLUME_TYPE_ENUM.QUANTITY"
            class="tw:tl:col-span-1"
          >
            <!-- 入荷量 -->
            <BaseLabel label="入荷量" isRequired />
            <BaseInput
              clearable
              type="text"
              maxlength="10"
              inputmode="numeric"
              autocomplete="nope"
              :model-value="form.quantity"
              @update:model-value="handleInputQuantity"
              v-cleave="{
                numeral: true,
                numeralPositiveOnly: true,
              }"
              outlined
              input-class="tw:text-right tw:text-xl-design"
              :error="!!errorStep2.quantity"
              :error-message="errorStep2.quantity"
            >
              <template v-slot:append>
                <div
                  :class="`tw:text-[#333333] tw:pr-4 tw:tl:pr-8 tw:text-m-design tw:mt-2`"
                >
                  尾
                </div>
              </template>
            </BaseInput>
          </div>
        </form>
      </q-tab-panel>
    </q-tab-panels>

    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
      tw:tl:justify-between tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        v-if="step === STEP_ENUM.STEP_1"
        outline

        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[28.01rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]`"
        label="入荷登録方法選択に戻る"
        @click.prevent="handleBack"
      />
      <BaseButton
        v-else
        outline
        padding="1.25rem"
        class="tw:rounded-[40px] tw:bg-white tw:text-blue-2 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[5.43rem] tw:tl:min-h-[5.43rem] tw:h-[5.43rem]
        tw:w-full"
        label="前に戻る"
        @click="step = STEP_ENUM.STEP_1"
      />
      <BaseButton
        outline

        class="tw:rounded-[40px]"
        :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="次に進む"
        @click.prevent="handleClickNext"
      />
    </q-footer>
  </div>
</template>

<script setup>
// #region import
import dayjs from 'boot/dayjs';
import BaseButton from 'components/base/vs/BaseButton.vue';
import useValidate from 'composables/validate';
import { FORMAT_NUMBER, doParseFloatNumber, isNumeric } from 'helpers/common';
import { storeToRefs } from 'pinia';
import commonService from 'services/common.service';
import BaseDatePicker from 'src/components/base/vs/BaseDatePicker.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import BaseLabel from 'src/components/base/vs/BaseLabel.vue';
import BasePartnerSelect from 'src/components/base/vs/BasePartnerSelect.vue';
import StepCustom from 'src/components/StepCustom.vue';
import { GENERATE_CODE_SUFFIX } from 'src/helpers/common';
import {
  ENTERPRISE_TYPE_ENUM,
  OPTION_TYPE_ENUM,
  STEP_ENUM,
  VOLUME_TYPE_ENUM,
} from 'src/helpers/constants';
import { registerManualStep1, registerManualStep2 } from 'src/schemas/arrival/registerManual.schema';
import { useConfirmFormStore } from 'src/stores/confirm-form-store';
import { useAppStore } from 'stores/app-store';
import { onMounted, provide, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

// #endregion import

// #region variable
const { setConfirmData, getConfirmData } = useConfirmFormStore();
const { settingUser, previousRoute } = storeToRefs(useAppStore());
const router = useRouter();
const { validateData: validateStep1, errors: errorStep1 } = useValidate();
const { validateData: validateStep2, errors: errorStep2 } = useValidate();
const unitPerGram = ref(settingUser.value.unit_per_gram ?? 0);
const INPUT_VOLUME_TYPE_OPTIONS = [
  { label: '重量', value: VOLUME_TYPE_ENUM.WEIGHT },
  { label: '尾数', value: VOLUME_TYPE_ENUM.QUANTITY },
];
const form = ref({
  mainCode: '0000000' + dayjs().format('YYMMDD'),
  subCode: '',
  supplier: '',
  date: dayjs().format('YYYY/MM/DD'),
  volumeType: VOLUME_TYPE_ENUM.WEIGHT,
  pricePerGram: '',
  grossWeight: '',
  tareWeight: '',
  netWeight: '',
  quantity: '',
});
const optionsPartner = ref([]);
const step = ref(STEP_ENUM.STEP_1);
const codeSuffixIdRegister = ref(null);
// #endregion variable

// #region action
const handleInputGrossWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(newValue || 0);
  const tareWeightNum = doParseFloatNumber(form.value.tareWeight || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.grossWeight = newValue;
  form.value.netWeight =
    netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum) : undefined;
  form.value.quantity =
    netWeightNum >= 0
      ? FORMAT_NUMBER(
          Math.ceil((netWeightNum / unitPerGram.value).toFixed(3))
        )
      : undefined;
};

const handleInputTareWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const grossWeightNum = doParseFloatNumber(
    form.value.grossWeight || 0
  );
  const tareWeightNum = doParseFloatNumber(newValue || 0);
  const netWeightNum = grossWeightNum - tareWeightNum;
  form.value.tareWeight = newValue;
  form.value.netWeight =
    netWeightNum >= 0 ? FORMAT_NUMBER(netWeightNum) : undefined;
  form.value.quantity =
    netWeightNum >= 0
      ? FORMAT_NUMBER(
          Math.ceil((netWeightNum / unitPerGram.value).toFixed(3))
        )
      : undefined;
};

const handleInputQuantity = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const quantityNum = doParseFloatNumber(newValue || 0);
  form.value.quantity = newValue;
  const netWeightNum =
    Math.ceil(
      quantityNum * (unitPerGram.value * 100)
    ) / 100;
  form.value.netWeight = newValue
    ? FORMAT_NUMBER(netWeightNum)
    : undefined;
  form.value.grossWeight = form.value.netWeight;
  form.value.tareWeight = undefined;
};

const handleSelectPartner = async newValue => {
  if (!newValue) {
    form.value.mainCode = '0000000' + dayjs(form.value.date).format('YYMMDD');
    form.value.subCode = '';
    codeSuffixIdRegister.value = null;
    return;
  } else {
    const partner = optionsPartner.value.find(item => item.value === newValue);
    switch (partner.enterprise_type) {
      case ENTERPRISE_TYPE_ENUM.FOREIGN:
        form.value.mainCode = `YUNYU**${dayjs(
          form.value.date
        ).format('YYMMDD')}`;
        break;
      case ENTERPRISE_TYPE_ENUM.FARM:
        form.value.mainCode = `JINKOU*${dayjs(
          form.value.date
        ).format('YYMMDD')}`;
        break;
      default:
        form.value.mainCode = `${partner?.enterprise_code}${dayjs(
          form.value.date
        ).format('YYMMDD')}`;
        break;
    }
  }
};

const handleBack = () => {
  router.back();
};

const handleClickNext = () => {
  if (step.value === STEP_ENUM.STEP_1) {
    const validate = validationStep1();
    if (!validate) {
      return;
    }
    step.value = STEP_ENUM.STEP_2;
  } else {
    const validate = validationStep2();
    if (!validate) {
      return;
    }

    const supplier = optionsPartner.value.find(
      item => item.value === form.value.supplier
    );
    setConfirmData({
      ...form.value,
      codeSuffixId: codeSuffixIdRegister.value,
      supplierName: supplier?.label || '',
    });
    router.push({
      name: 'manualRegistrationConfirm',
    });
  }
};

const handleChangeVolumeType = newValue => {
  form.value.volumeType = newValue;
  errorStep2.value = {};
  form.value.grossWeight = '';
  form.value.tareWeight = '';
  form.value.quantity = '';
  form.value.netWeight = '';
};
// #endregion

// #region helpers
const maskMainCode = value => {
  if (value?.length !== 13) {
    return '';
  }
  const part1 = value.slice(0, 7);
  const part2 = value.slice(7, 13);
  return `${part1}-${part2}-`;
};

const validationStep1 = () => {
  const payload = {
    mainCode: form.value.mainCode,
    subCode: form.value.subCode,
    supplier: form.value.supplier,
    date: form.value.date,
    volumeType: form.value.volumeType,
  };

  return validateStep1(registerManualStep1, payload);
};

const validationStep2 = () => {
  const payload = {
    grossWeight: form.value.grossWeight ? doParseFloatNumber(form.value.grossWeight) : '',
    tareWeight: form.value.tareWeight ? doParseFloatNumber(form.value.tareWeight) : 0,
    quantity: form.value.quantity ? doParseFloatNumber(form.value.quantity) : '',
  };
  return validateStep2(registerManualStep2, payload);
};
// #endregion

// #region watch
watch(
  () => form.value.date,
  () => {
    if (
      /^(19|20)\d{2}\/(0[1-9]|1[0-2])\/(0[1-9]|[12]\d|3[01])$/.test(
        form.value.date
      )
    ) {
      form.value.mainCode =
        form.value.mainCode.substring(0, 7) +
        dayjs(form.value.date).format('YYMMDD');
    }
  }
);

watch(
  () => form.value.mainCode,
  async () => {
    if (form.value.supplier) {
      const codeSuffixResponse = await GENERATE_CODE_SUFFIX(form.value.mainCode);
      codeSuffixIdRegister.value = codeSuffixResponse.id;
      form.value.subCode = codeSuffixResponse.code_suffix;
    }
  }
);
// #endregion watch

provide('basePartnerSelectProvideData', { listPartner: optionsPartner });

onMounted(async () => {
  const partnerOptionsResponse = await commonService.getOptions({
    type: OPTION_TYPE_ENUM.USER_SUPPLIER,
  });
  optionsPartner.value = partnerOptionsResponse.payload;

  // get data from confirm form store
  const shipmentStoreData = getConfirmData();
  // if shipmentStoreData is not empty, set form values
  if (
    shipmentStoreData &&
    previousRoute.value.name === 'manualRegistrationConfirm'
  ) {
    const { codeSuffixId, supplierName, ...rest } = shipmentStoreData;
    // pass rest of the data to form
    form.value = {
      ...form.value,
      ...rest,
    };
    // set codeSuffixId
    codeSuffixIdRegister.value = codeSuffixId;
  }
});
</script>

<style scoped>
:deep(.q-field--outlined .q-field__control) {
  padding-right: 0;
}

:deep(.q-field__control-container .q-field__suffix) {
  margin-right: 1rem !important;
}
</style>
