<template>
  <q-page class="h-full tw:text-[#333333]">
    <!-- filter start -->
    <div class="tw:my-4">
      <ExpansionSearchItem>
        <SearchForm />
      </ExpansionSearchItem>
    </div>
    <div v-if="
      searchConditions.supplier || searchConditions.startArrivalDate ||
      searchConditions.endArrivalDate || searchConditions.licenseNumber ||
      searchConditions.note1 || searchConditions.note2 ||
      searchConditions.enterpriseName || searchConditions.enterpriseCode
    " class="tw:mt-4 tw:mb-4
      tw:text-xs-design tw:flex tw:items-center tw:space-x-2">
      <span class="tw:font-bold tw:text-m-design tw:text-[#004AB9]">検索条件</span>
      <span v-if="searchConditions.enterpriseName"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white">
        {{ searchConditions.enterpriseName }}
      </span>
      <span v-if="searchConditions.supplier"
        class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white">
        {{ searchConditions.supplier }}
      </span>
      <div
        v-if="searchConditions.startArrivalDate || searchConditions.endArrivalDate"
       class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white">
        <span v-if="searchConditions.startArrivalDate">{{ searchConditions.startArrivalDate }}</span>
        <span class="tw:mx-2" v-if="searchConditions.startArrivalDate && searchConditions.endArrivalDate">～</span>
        <span v-if="searchConditions.endArrivalDate">{{ searchConditions.endArrivalDate }}</span>
      </div>
      <span v-if="searchConditions.enterpriseCode" class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white">
        {{ searchConditions.enterpriseCode }}
      </span>
      <span v-if="searchConditions.licenseNumber" class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white">
        {{ searchConditions.licenseNumber }}
      </span>
      <span v-if="searchConditions.note1" class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white">
        {{ searchConditions.note1 }}
      </span>
      <span v-if="searchConditions.note2" class="tw:text-xs-design tw:ml-[1rem] tw:p-2 tw:rounded tw:bg-white">
        {{ searchConditions.note2 }}
      </span>
    </div>
     <!-- filter end -->
    <q-card class="tw:p-4 tw:pb-[22rem]">
      <!-- sort btn start -->
      <div class="tw:flex tw:tl:flex-row tw:flex-col tw:justify-between tw:border-neutral-500 tw:pt-1">
        <div class="tw-flex tw:tl:flex-row tw:flex-col tw:items-start tw:space-y-4">
          <div class="tw:flex tw:flex-row tw:items-center tw:space-x-2 tw:h-[4.25rem]">
            <span class="tw:text-xs-design tw:mr-3">表示件数</span>
            <PageSizeDropdownSP v-model="pagination.limit" />
          </div>
          <SortByDropdownSP
            class="tw:text-xs-design tw:space-x-2 tw:h-[4.25rem] tw:tl:hidden tw:mb-[1rem] tw:tl:mb-0" />
        </div>
        <div class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-0">
          <PaginationNotifi />
        </div>
      </div>
      <!-- sort btn end -->
      <!-- start table for tablet -->
      <div class="tw:hidden tw:tl:block q-py-sm">
        <q-table
          :rows="sortedRows"
          :columns="columns"
          row-key="index"
          hide-pagination
          bordered
          v-model:pagination="paginationComputed"
        >
          <template v-slot:header="props">
            <q-tr :props="props" class="tw:text-s-design" :class="`tw:bg-[#E2E3EA]`">
              <q-th
              @click="handleClickSort(props.cols[0].name)"
                class="tw:font-normal tw:text-s-design tw:text-left tw:border-r tw-border-[#D2D2D2] tw:tl:w-[35%] tw:dt:w-[30%]"
              >
                {{ props.cols[0].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[0].name)"
                  :name="
                    getSortOrder(props.cols[0].name) === 'asc'
                      ? 'arrow_upward'
                      : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                @click="handleClickSort(props.cols[1].name)"
                class="tw:font-normal tw:text-s-design tw:text-left tw:border-r tw-border-[#D2D2D2] tw:tl:w-[35%] tw:dt:w-[30%]"
              >
                {{ props.cols[1].label }}
                <q-icon
                  v-if="getSortOrder(props.cols[1].name)"
                  :name="
                    getSortOrder(props.cols[1].name) === 'asc'
                      ? 'arrow_upward'
                      : 'arrow_downward'
                  "
                  size="16px"
                  class="tw:ml-1"
                />
              </q-th>
              <q-th
                class="tw:font-normal tw:text-s-design tw:text-left  tw:border-r tw-border-[#D2D2D2] tw:tl:w-[15%]"
              >
                {{ props.cols[2].label }}
              </q-th>
              <q-th
                class="tw:font-normal tw:text-s-design tw:text-left  tw:border-r tw-border-[#D2D2D2] tw:tl:w-[15%]"
              >
                {{ props.cols[3].label }}
              </q-th>
              <q-th
                class="tw:font-normal tw:text-s-design tw:text-left  tw:border-r tw-border-[#D2D2D2] tw:tl:w-[5%]"
              >
                {{ props.cols[4].label }}
              </q-th>
            </q-tr>
          </template>
          <template v-slot:body="props">
            <q-tr
              class="tw:cursor-pointer tw:w-full"
              :props="props"
              @click.prevent="arrivalDetail(props.row)"
            >
              <q-td
                key="destination_user_name"
                :props="props"
                class="text-left border-r border-l border-b border-td-color tw:text-s-design tw:border-r tw-border-[#D2D2D2]"
              >
                <div
                  :class="`tw:truncate tw:tl:max-w-[300px] tw:lg:max-w-[350px]
                    tw:xl:max-w-[540px] tw:dt:max-w-[490px] tw:underline tw:underline-offset-1 tw:text-[#004AB9]`"
                >
                  {{ props.row.destination_user_name }}
                </div>
              </q-td>
              <q-td
                key="destination_enterprise_name"
                :props="props"
                class="text-left border-r border-l border-b border-td-color tw:text-s-design tw:border-r tw-border-[#D2D2D2]"
              >
                <div
                  class="tw:truncate tw:dt:w-[15rem] tw:tl:w-[15rem]"
                >
                  {{ props.row.destination_enterprise_name }}
                </div>
              </q-td>
              <q-td
                key="arrival_date"
                :props="props"
                class="text-left border-r border-l border-b border-td-color tw:text-s-design tw:border-r tw-border-[#D2D2D2]"
              >
                <div
                  class="tw:truncate tw:dt:w-[16rem] tw:tl:w-[16rem]"
                >
                  {{ FORMAT_DATE(props.row.arrival_date) }}
                </div>
              </q-td>
              <q-td
                key="code"
                :props="props"
                class="border-r border-b border-td-color tw:text-[1.6rem]"
              >
                <div class="tw:truncate tw:dt:w-[16rem] tw:tl:w-[16rem]">
                  {{ maskCodeString(props.row.code) }}
                </div>
              </q-td>
              <q-td
                key="arrival_net_weight"
                :props="props"
                class="text-left border-r border-l border-b border-td-color tw:text-s-design tw:border-r tw-border-[#D2D2D2]"
              >
                <div class="tw:truncate tw:dt:w-[16rem] tw:tl:w-[16rem]">
                  {{ FORMAT_NUMBER(props.row.arrival_net_weight) }}g
                </div>
              </q-td>
              <q-td
                key="is_staff"
                :props="props"
                class="text-center border-r border-l border-b border-td-color tw:text-s-design tw:border-r tw-border-[#D2D2D2]"
              >
                <div class="tw:truncate">
                  <q-icon
                    name="person_outline"
                    class="icon-custom tw:text-s-design"
                    v-if="
                      CHECK_ROLE(
                        [ROLES_ENUM.NORMAL_USER],
                        [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
                        [STAFF_TYPE_ENUM.ENTERPRISE],
                        user
                      ) &&
                      !CHECK_ROLE(
                        [ROLES_ENUM.NORMAL_USER],
                        [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
                        [STAFF_TYPE_ENUM.ENTERPRISE],
                        props.row.destination_user
                      )
                    "
                    size="2rem"
                  />
                </div>
              </q-td>
            </q-tr>
          </template>
          <template v-slot:no-data="">
            <div
              class="tw:w-full tw:text-center tw:text-s-design"
            >
              データが見つかりません。
            </div>
          </template>
        </q-table>
      </div>
      <!-- start table for smartphone -->
      <div class="tw:tl:hidden tw:block q-py-sm">
        <q-table
          grid
          card-container-class="tw:flex-col tw:gap-1"
          card-class="tw:w-full"
          :rows="listArrival"
          :columns="columns"
          row-key="index"
          hide-pagination
          hide-header
          v-model:pagination="paginationComputed"
        >
          <template v-slot:item="props">
            <div class="tw:w-full">
              <q-card flat bordered>
                <q-card-section class="tw:text-left tw:cursor-pointer tw:flex tw:justify-between tw:items-center"
                  @click.prevent="arrivalDetail(props.row)">
                  <div>
                    <strong class="tw:text-s-design tw:text-[#004AB9] tw:break-all">
                      {{
                        calcDestinationName(props.row)
                      }}</strong>
                    <br />
                    <div class="tw:text-[#333333] tw:text-s-design tw:w-full">
                      <!-- {{ props.cols[1].label }}: {{ FORMAT_DATE(props.row.arrival_date) }} -->
                      <!-- <br /> -->
                      {{ props.cols[2].label }}: {{ maskCodeString(props.row.code) }}
                      <br />
                      <div class="tw:flex tw:justify-between tw:items-center tw:text-[#333333] tw:text-s-design">
                        <span>
                          {{ props.cols[3].label }}: {{ FORMAT_NUMBER(props.row.arrival_net_weight)}}
                          g
                        </span>
                      </div>
                    </div>
                  </div>

                  <q-icon
                    name="person_outline"
                    class="icon-custom"
                    style="color: #7E8093;"
                    v-if="
                      CHECK_ROLE(
                        [ROLES_ENUM.NORMAL_USER],
                        [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
                        [STAFF_TYPE_ENUM.ENTERPRISE],
                        user
                      ) &&
                      !CHECK_ROLE(
                        [ROLES_ENUM.NORMAL_USER],
                        [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
                        [STAFF_TYPE_ENUM.ENTERPRISE],
                        props.row.destination_user
                      )
                    "
                    size="3.3rem"
                  />
                </q-card-section>
              </q-card>
            </div>
          </template>
          <template v-slot:no-data="">
            <div
              class="tw:w-full tw:text-center tw:text-s-design"
            >
              データが見つかりません。
            </div>
          </template>
        </q-table>
      </div>
      <div class="tw:tl:justify-end tw:flex tw:justify-center tw:tl:col-span-3 tw:pt-5 tw:tl:pt-2">
        <PaginationNotifi />
      </div>
      <!-- table end -->
    </q-card>
    <div class="tw:relative tw:overflow-hidden tw:h-0">
      <ArrivalListPdf />
    </div>

    <q-footer elevated class="tw:bg-white tw:p-3
    tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
     tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row">
      <BaseButton outline class="tw:rounded-[40px]" :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`" label="トップに戻る" @click.prevent="goToPage('home')" />
      <div class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:gap-4 tw:w-full tw:tl:w-[60rem]">
        <BaseButton outline class="tw:rounded-[40px]" :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
      tw:tl:w-[30.25rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`" label="実績をPDFでダウンロード" @click.prevent="exportPdfHandler"/>
        <BaseButton outline class="tw:rounded-[40px]" :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
      tw:tl:w-[30rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`" label="実績をCSVでダウンロード" @click.prevent="exportCsvHandler"/>
      </div>
    </q-footer>
  </q-page>
</template>

<script setup>
// #region import
import { onMounted, provide, ref, watch, nextTick, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter, useRoute } from 'vue-router';
import {
  FORMAT_DATE,
  FORMAT_NUMBER,
  exportCSV,
  FORMAT_DATE_TIME_CSV,
  maskCodeString,
} from 'helpers/common';
import { ROLES_ENUM } from 'helpers/constants';
import useValidate from 'composables/validate';
import searchAllArrivalList from 'schemas/searchAllArrivalList';
import arrivalService from 'services/arrival.service';
import ArrivalListPdf from 'components/pdf/ArrivalListPdf.vue';
import html2pdf from 'html2pdf.js';
import ExpansionSearchItem from 'components/ExpansionSearchItem.vue';
import { useAuthStore } from 'src/stores/auth-store';
import { isEqual, orderBy } from 'lodash';
import { useLocalStorage } from 'composables/localstorage';
import PageSizeDropdownSP from 'src/components/PageSizeDropdownSP.vue';
import PaginationNotifi from 'src/components/PaginationNotifi.vue';
import {
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
} from 'src/helpers/constants';
import { CHECK_ROLE } from 'src/helpers/common';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import SortByDropdownSP from 'components/SortByDropdownSP.vue';

import SearchForm from './components/SearchForm.vue';

// #endregion import

// #region variable
const router = useRouter();
const { setLoadingManual, setLoading } = useAppStore();
const { user } = storeToRefs(useAuthStore());
const { errors, validateData } = useValidate();
const { listPageQueryParams } = useLocalStorage();
const route = useRoute();

const columns = [
  {
    name: 'destination_user_name',
    label: '仕入先(事業者名)',
    align: 'left',
    field: 'destination_user_name',
    sortable: true,
  },
  {
    name: 'destination_enterprise_name',
    label: '仕入先(届出事業者名)',
    align: 'left',
    field: 'destination_enterprise_name',
    sortable: true,
  },
  {
    name: 'arrival_date',
    label: '入荷日',
    align: 'left',
    field: 'arrival_date',
    sortable: true,
  },
  // {
  //   name: 'code',
  //   align: 'left',
  //   label: '漁獲/荷口番号',
  //   field: 'code',
  //   sortable: true,
  // },
  {
    name: 'arrival_net_weight',
    align: 'right',
    label: '入荷量',
    field: 'arrival_net_weight',
    sortable: true,
  },
  {
    name: 'is_staff',
    align: 'center',
    label: '',
    field: 'is_staff',
    sortable: true,
  },
];

const searchConditions = computed(() => {
  const query = route.query;
  return {
    supplier: query.supplier || '',
    enterpriseName: query.enterpriseName || '',
    startArrivalDate: query.startArrivalDate || '',
    endArrivalDate: query.endArrivalDate || '',
    licenseNumber: query.licenseNumber || '',
    enterpriseCode: query.enterpriseCode || '',
    note1: query.note1 || '',
    note2: query.note2 || '',
  };
});

const pagination = ref({
  sortBy: 'id',
  descending: false,
  page: 1,
  limit: listPageQueryParams.value?.arrivalList?.limit || 10,
});
const totalPage = ref(1);
const pageSize = ref(10);
const listArrival = ref([]);
const dataExport = ref([]);
const dataExportPdf = ref([]);
const isShowExport = ref(false);
const flagDownload = ref(false);
const sortBySelectedLabel = ref('新着順');
const multiSortConditions = ref([]);

const searchForm = ref({
  supplier: '',
  startArrivalDate: '',
  endArrivalDate: '',
  licenseNumber: '',
  note1: '',
  note2: '',
  enterpriseName: '',
  enterpriseCode: '',
});
const searchFormData = ref({
  supplier: '',
  startArrivalDate: '',
  endArrivalDate: '',
  licenseNumber: '',
  note1: '',
  note2: '',
  enterpriseName: '',
  enterpriseCode: '',
});
const pageIndex = ref(1);
const hasDiffPageIndex = ref(false);

// #endregion variable

// #region function
const arrivalDetail = async row => {
  await router.push({
    name: 'arrivalDetail',
    params: {
      id: row.id,
    },
  });
};

const formatSearchFormToQuery = form => ({
  supplier: form.supplier || undefined,
  startArrivalDate: form.startArrivalDate || undefined,
  endArrivalDate: form.endArrivalDate || undefined,
  licenseNumber: form.licenseNumber || undefined,
  note1: form.note1 || undefined,
  note2: form.note2 || undefined,
  enterpriseName: form.enterpriseName || undefined,
  enterpriseCode: form.enterpriseCode || undefined,
});

// calc destination name for render table
const calcDestinationName = item => {
  // if destination is enterprise
  if (
    CHECK_ROLE(
      [ROLES_ENUM.NORMAL_USER],
      [
        ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
      ],
      [STAFF_TYPE_ENUM.ENTERPRISE],
      item.destination_user
    ) ||
    CHECK_ROLE(
      [ROLES_ENUM.NORMAL_USER],
      [ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
      [],
      item.destination_user
    )
  ) {
    return item.destination_enterprise_name;
  }

  return item.destination_user_name
    ? `${item.destination_user_name}(${item.destination_enterprise_name})`
    : item.destination_enterprise_name;
};

const getData = async (isExport = false, isCSV = false) => {
  flagDownload.value = false;
  const searchCondition = {
    ...pagination.value,
    page: isExport ? -1 : pagination.value.page,
    supplier: searchFormData.value.supplier || undefined,
    licenseNumber: searchFormData.value.licenseNumber || undefined,
    startArrivalDate: searchFormData.value.startArrivalDate || undefined,
    endArrivalDate: searchFormData.value.endArrivalDate || undefined,
    note1: searchFormData.value.note1 || undefined,
    note2: searchFormData.value.note2 || undefined,
    enterpriseName: searchFormData.value.enterpriseName || undefined,
    enterpriseCode: searchFormData.value.enterpriseCode || undefined,
  };
  const result = await arrivalService.getArrivalList(searchCondition);
  isShowExport.value = false;
  if (result.code === 0) {
    listArrival.value = result.payload?.items ?? [];
    if (window.innerWidth < 960) {
      let key;
      const order = 'asc';
      switch (router.currentRoute.value.query.sortBy) {
        case '':
          key = 'id';
          break;
        case 'destination_enterprise_name':
          key = 'destination_enterprise_name';
          break;
        case 'destination_user_name':
          key = 'destination_user_name';
          break;
        case 'arrival_net_weight':
          key = 'arrival_net_weight';
          break;
        default:
          break;
      }

      listArrival.value = orderBy(listArrival.value, [key], [order]);
    }

    if (!isExport) {
      listArrival.value = result.payload.items;
      totalPage.value = Math.ceil(
        (result.payload.total_item ?? 1) / pagination.value.limit
      );

      if (result.payload.page !== pageIndex.value) {
        hasDiffPageIndex.value = true;
        router.replace({
          query: {
            ...pagination.value,
            page: result.payload.page,
            ...formatSearchFormToQuery(searchFormData.value),
          },
        });
      }
    } else {
      flagDownload.value = true;
      dataExport.value = result.payload.items.map(item => ({
        arrival_date: FORMAT_DATE(item.arrival_date),
        destination_enterprise_name:
          !item.destination_user_name ||
          CHECK_ROLE(
            [ROLES_ENUM.NORMAL_USER],
            [
              ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
              ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
            ],
            [STAFF_TYPE_ENUM.ENTERPRISE],
            item.destination_user
          )
            ? `${item.destination_enterprise_name}${
                !(
                  item.destination_user?.enterprise_type ===
                    ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
                  item.destination_user.staff_type === STAFF_TYPE_ENUM.STAFF
                ) && isCSV
                  ? '※'
                  : ''
              }`
            : `${item.destination_user_name} (${
                item.destination_enterprise_name
              })${
                !(
                  item.destination_user?.enterprise_type ===
                    ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
                  item.destination_user.staff_type === STAFF_TYPE_ENUM.STAFF
                ) && isCSV
                  ? '※'
                  : ''
              }`,
        license_number: item.destination_license_number ?? '',
        code: item.code ? maskCodeString(item.code) : '',
        destination_user_name: item.destination_user_name,
        arrival_net_weight: item.arrival_net_weight,
        mark_staff: !(
          item.destination_user?.enterprise_type ===
            ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
          item.destination_user.staff_type === STAFF_TYPE_ENUM.STAFF
        )
          ? '※'
          : '',
        catch_code: item.shipping_id_list ? item.shipping_id_list
          .map(value => maskCodeString(value?.code || '')).join(',') : '',
      }));
    }
  };

  if (
    listArrival.value.length > 0 &&
    (searchFormData.value.supplier ||
      searchFormData.value.startArrivalDate ||
      searchFormData.value.endArrivalDate ||
      searchFormData.value.licenseNumber ||
      searchFormData.value.enterpriseName ||
      searchFormData.value.enterpriseCode ||
      searchFormData.value.note1 ||
      searchFormData.value.note2)
  ) {
    isShowExport.value = true;
  }
};

const exportCsvHandler = async () => {
  // download csv
  await getData(true, true);
  if (!flagDownload.value) {
    return;
  }
  exportCSV(
    dataExport.value,
    [
      'arrival_date',
      'destination_enterprise_name',
      'license_number',
      'code',
      'destination_user_name',
      'arrival_net_weight',
      'reason_diff',
    ],
    [
      '入荷日',
      '仕入先※事業者',
      '許可番号',
      '漁獲番号/荷口番号',
      '入荷者(届出事業者)',
      '入荷量[g]',
      '差異の理由',
    ],
    `シラスウナギ入荷実績_${FORMAT_DATE_TIME_CSV()}`
  );
};

const exportPdfHandler = async () => {
  // download pdf
  await getData(true, false);
  if (!flagDownload.value) {
    return;
  }
  dataExportPdf.value = dataExport.value;
  nextTick(async () => {
    setLoadingManual(true);
    setLoading(true);
    const elementBody = document.getElementById('large-body-pdf');
    const rows = Array.from(elementBody.children);

    const worker = html2pdf().set({
      filename: `シラスウナギ入荷実績_${FORMAT_DATE_TIME_CSV()}.pdf`,
      margin: [0.5, 0.1, 0.6, 0.1],
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 1 },
      jsPDF: { unit: 'in', format: 'a4', orientation: 'landscape' },
    });

    const processBatches = async () => {
      for (let i = 0; i < rows.length; ) {
        let batchHeight = 0;
        let rowIndex = i;
        const batchElement = document.createElement('div');
        while (rowIndex < rows.length) {
          const row = rows[rowIndex];
          const rowHeight = row.getBoundingClientRect().height;
          if (batchHeight + rowHeight > 31000) {
            break;
          }
          batchHeight += rowHeight;
          rowIndex += 1;
          batchElement.appendChild(row.cloneNode(true));
        }

        const batchSize = rowIndex - i;
        i += batchSize;

        await worker
          .from(batchElement)
          .toContainer()
          .toCanvas()
          .toPdf()
          .get('pdf')
          .then(pdf => {
            if (i + batchSize < rows.length) {
              pdf.addPage();
            }
          });

        await worker.get('canvas').then(canvas => {
          // reset canvas size
          canvas.height = 0;
          canvas.width = 0;
          canvas.style.height = '0px';
          canvas.style.width = '0px';
        });
      }

      await worker.save();
      setLoading(false);
      setLoadingManual(false);
    };

    await processBatches();
  });
};

const sortedRows = computed(() => {
  const rows = [...listArrival.value];
  const lstKey = [];
  const lstOrder = [];
  for (const { key, order } of multiSortConditions.value) {
    lstKey.push(key);
    lstOrder.push(order);
  }
  return orderBy(rows, lstKey, lstOrder);
});

const handleClickSort = key => {
  const idx = multiSortConditions.value.findIndex(i => i.key === key);

  if (idx >= 0) {
    const current = multiSortConditions.value[idx];

    if (current.order === 'asc') {
      current.order = 'desc';
      multiSortConditions.value.splice(idx, 1);
      multiSortConditions.value.unshift(current);
    } else if (current.order === 'desc') {
      multiSortConditions.value.splice(idx, 1);
    }
  } else {
    multiSortConditions.value.unshift({ key, order: 'asc' });
  }
};

const getSortOrder = key => {
  const condition = multiSortConditions.value.find(i => i.key === key);
  return condition?.order;
};

const goToPage = name => {
  router.push({ name });
};
// #endregion function

// #region computed
const paginationComputed = computed(() => ({
  page: 1,
  rowsPerPage: +pagination.value.limit,
}));
// #endregion

// #region watch
watch(pageSize, async () => {
  pagination.value.limit = pageSize.value;
  router.push({
    query: {
      ...pagination.value,
      ...formatSearchFormToQuery(searchFormData.value),
      page: 1,
    },
  });
});

watch(
  () =>
    router.currentRoute.value.query.sortBy +
    router.currentRoute.value.query.descending +
    router.currentRoute.value.query.page +
    router.currentRoute.value.query.limit +
    router.currentRoute.value.query.supplier +
    router.currentRoute.value.query.licenseNumber +
    router.currentRoute.value.query.startArrivalDate +
    router.currentRoute.value.query.endArrivalDate +
    router.currentRoute.value.query.enterpriseName +
    router.currentRoute.value.query.enterpriseCode +
    router.currentRoute.value.query.note1 +
    router.currentRoute.value.query.note2,
  async () => {
    if (router.currentRoute.value.query.limit) {
      listPageQueryParams.value.arrivalList = {
        limit: +router.currentRoute.value.query.limit,
      };
    }

    pageSize.value = +router.currentRoute.value.query.limit;
    pageIndex.value = +router.currentRoute.value.query.page;
    sortBySelectedLabel.value = router.currentRoute.value.query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(
          item => item.value === router.currentRoute.value.query.sortBy
        ).label
      : '新着順';
    pagination.value = {
      ...pagination.value,
      page: router.currentRoute.value.query.page,
      limit: router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      descending: router.currentRoute.value.query.descending === 'true',
    };
    searchForm.value = {
      supplier: router.currentRoute.value.query.supplier || '',
      startArrivalDate: router.currentRoute.value.query.startArrivalDate || '',
      endArrivalDate: router.currentRoute.value.query.endArrivalDate || '',
      licenseNumber: router.currentRoute.value.query.licenseNumber || '',
      note1: router.currentRoute.value.query.note1 || '',
      note2: router.currentRoute.value.query.note2 || '',
      enterpriseName: router.currentRoute.value.query.enterpriseName || '',
      enterpriseCode: router.currentRoute.value.query.enterpriseCode || '',
    };
    searchFormData.value = {
      supplier: router.currentRoute.value.query.supplier || '',
      startArrivalDate: router.currentRoute.value.query.startArrivalDate || '',
      endArrivalDate: router.currentRoute.value.query.endArrivalDate || '',
      licenseNumber: router.currentRoute.value.query.licenseNumber || '',
      note1: router.currentRoute.value.query.note1 || '',
      note2: router.currentRoute.value.query.note2 || '',
      enterpriseName: router.currentRoute.value.query.enterpriseName || '',
      enterpriseCode: router.currentRoute.value.query.enterpriseCode || '',
    };

    listPageQueryParams.value.arrivalList = {
      ...listPageQueryParams.value.arrivalList,
      ...searchFormData.value,
      sortBy: router.currentRoute.value.query.sortBy,
    };
    if (window.innerWidth < 960) {
    let key;
    const order = 'asc';
    switch (router.currentRoute.value.query.sortBy) {
      case '':
        key = 'id';
        break;
      case 'destination_enterprise_name':
        key = 'destination_enterprise_name';
        break;
      case 'destination_user_name':
        key = 'destination_user_name';
        break;
      case 'arrival_net_weight':
        key = 'arrival_net_weight';
        break;
      default:
        break;
    }

    listArrival.value = orderBy(listArrival.value, [key], [order]);
  }
    if (!hasDiffPageIndex.value) {
      await getData();
    } else {
      hasDiffPageIndex.value = false;
    }
  }
);
// #endregion watch

// #region provide
provide('arrivalListDataExport', dataExport);
provide('arrivalListDataExportPdf', dataExportPdf);
provide('dataSearchExport', searchFormData);

const expansionSearchProvideData = {
  handleClear() {
    searchForm.value = {
      supplier: '',
      enterpriseName: '',
      startArrivalDate: '',
      endArrivalDate: '',
      licenseNumber: '',
      enterpriseCode: '',
      note1: '',
      note2: '',
    };
    errors.value = {};
  },
  async handleSearch() {
    if (isEqual({ ...searchForm.value }, { ...searchFormData.value })) {
      await getData();
      return;
    }

    searchForm.value = {
      ...searchForm.value,
      licenseNumber: searchForm.value.licenseNumber,
      note1: searchForm.value.note1,
      note2: searchForm.value.note2,
      enterpriseCode: searchForm.value.enterpriseCode,
    };

    const data = {
      ...searchForm.value,
      licenseNumber: searchForm.value.licenseNumber || undefined,
      note1: searchForm.value.note1 || undefined,
      note2: searchForm.value.note2 || undefined,
      enterpriseCode: searchForm.value.enterpriseCode || undefined,
    };

    const validate = validateData(searchAllArrivalList, {
      ...data,
    });

    if (validate) {
      pageIndex.value = 1;
      await router.push({
        query: {
          ...pagination.value,
          ...formatSearchFormToQuery(searchForm.value),
          page: 1,
        },
      });
    }
  },
  title: '入荷実績をさがす',
};
provide('expansionSearchProvideData', expansionSearchProvideData);

const searchFormProvideData = {
  form: searchForm,
  errors,
};
provide('searchFormProvideData', searchFormProvideData);

const sortByDropdownProvideData = {
  sortByOptions: [
    { label: '新着順', value: 'id' },
    { label: '仕入先(届出事業者名)', value: 'destination_enterprise_name' },
    { label: '仕入先(事業者名)', value: 'destination_user_name' },
    { label: '入荷量順', value: 'arrival_net_weight' },
  ],
  sortBySelectedLabel,
  handleClickSortByItem: async option => {
    sortBySelectedLabel.value = option.label;
    pagination.value.sortBy = option.value;
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
        page: 1,
      },
    });
  },
};
provide('sortByDropdownProvideData', sortByDropdownProvideData);

const paginationItemProvideData = {
  pageIndex,
  totalPage,
  onChangedPageIndex: async value => {
    pagination.value = {
      ...pagination.value,
      page: value,
    };
    await router.push({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  },
};
provide('paginationItemProvideData', paginationItemProvideData);
// #endregion provide

onMounted(async () => {
  if (!router.currentRoute.value?.query?.page) {
    router.replace({
      query: {
        ...pagination.value,
        ...formatSearchFormToQuery(searchFormData.value),
      },
    });
  } else {
    pageSize.value = +router.currentRoute.value.query.limit;
    pageIndex.value = +router.currentRoute.value.query.page;
    sortBySelectedLabel.value = router.currentRoute.value.query.sortBy
      ? sortByDropdownProvideData.sortByOptions.find(
          item => item.value === router.currentRoute.value.query.sortBy
        ).label
      : '新着順';
    pagination.value = {
      ...pagination.value,
      page: +router.currentRoute.value.query.page,
      limit: +router.currentRoute.value.query.limit,
      sortBy: router.currentRoute.value.query.sortBy,
      descending: router.currentRoute.value.query.descending === 'true',
    };
    searchForm.value = {
      supplier: router.currentRoute.value.query.supplier || '',
      startArrivalDate: router.currentRoute.value.query.startArrivalDate || '',
      endArrivalDate: router.currentRoute.value.query.endArrivalDate || '',
      licenseNumber: router.currentRoute.value.query.licenseNumber || '',
      note1: router.currentRoute.value.query.note1 || '',
      note2: router.currentRoute.value.query.note2 || '',
      enterpriseName: router.currentRoute.value.query.enterpriseName || '',
      enterpriseCode: router.currentRoute.value.query.enterpriseCode || '',
    };
    searchFormData.value = {
      supplier: router.currentRoute.value.query.supplier || '',
      startArrivalDate: router.currentRoute.value.query.startArrivalDate || '',
      endArrivalDate: router.currentRoute.value.query.endArrivalDate || '',
      licenseNumber: router.currentRoute.value.query.licenseNumber || '',
      note1: router.currentRoute.value.query.note1 || '',
      note2: router.currentRoute.value.query.note2 || '',
      enterpriseName: router.currentRoute.value.query.enterpriseName || '',
      enterpriseCode: router.currentRoute.value.query.enterpriseCode || '',
    };
    await getData();
  }
});
</script>

<style scoped>
.icon-custom {
  color: #7E8093;
}
:deep(.q-checkbox__label) {
  font-size: 2rem;
  line-height: 1.5rem;
}
</style>
