import <PERSON><PERSON><PERSON><PERSON> from 'helpers/message';
import {
  INCLUDE_TAX_TYPE_OPTIONS,
  OPTIONS_INVENTORY_CONTROL_TYPE,
  RECEIPT_NUMBER_VALUE_ENUM,
  REPORT_TYPE_ENUM,
  SHOW_DEFAULT_SCAN_QR_ENUM,
} from 'src/helpers/constants';

const updateSettingSchema = {
  type: 'object',
  additionalProperties: false,
  required: [
    'enable_session_timeout',
    'display_actual_received',
    'qr_scan_init',
    'inventory_control_type',
    'unit_per_gram',
    'report_type',
    'display_shipment_weight',
    'price_per_kilogram',
    'price_per_quantity',
    'include_tax_type',
    'receipt_number',
  ],
  properties: {
    enable_session_timeout: {
      type: 'boolean',
    },
    session_expirytime: {
      type: 'integer',
      minimum: 1,
      maximum: 24,
      not: { const: '' },
      errorMessage: {
        not: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        minimum: MESSAGE.MSG_LIMITS_AUTOLOGOUTTIME_ERROR,
        maximum: MESSAGE.MSG_LIMITS_AUTOLOGOUTTIME_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    display_actual_received: {
      type: 'boolean',
    },
    qr_scan_init: {
      type: 'integer',
      enum: Object.values(SHOW_DEFAULT_SCAN_QR_ENUM),
    },
    destination_id: {
      type: 'integer',
    },
    inventory_control_type: {
      type: 'integer',
      enum: OPTIONS_INVENTORY_CONTROL_TYPE.map(item => item.value),
    },
    unit_per_gram: {
      type: 'number',
      exclusiveMinimum: 0.01,
      not: { const: '' },
      maximum: 999.99,
      checkFormatDecimal: true,
      errorMessage: {
        exclusiveMinimum: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
        not: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
        _: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
      },
    },
    report_type: {
      type: 'integer',
      enum: Object.values(REPORT_TYPE_ENUM),
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    display_shipment_weight: {
      type: 'boolean',
    },
    price_per_kilogram: {
      type: 'array',
      maxItems: 3,
      minItems: 1,
      items: {
        type: ['number', 'null'],
        exclusiveMinimum: 0,
        maximum: 99999999,
        checkFormatDecimal: true,
        errorMessage: {
          exclusiveMinimum: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
          _: MESSAGE.MSG_SAFE_DIGITS_ERROR,
        },
      },
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    lst_kg: {
      type: 'array',
      maxItems: 3,
      minItems: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    price_per_quantity: {
      type: 'array',
      maxItems: 3,
      minItems: 1,
      items: {
        type: ['number', 'null'],
        exclusiveMinimum: 0,
        maximum: 99999,
        checkFormatDecimal: true,
        errorMessage: {
          exclusiveMinimum: MESSAGE.MSG_LIMITS_NUMBER_ERROR,
          _: MESSAGE.MSG_SAFE_DIGITS_ERROR,
        },
      },
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    lst_quantity: {
      type: 'array',
      maxItems: 3,
      minItems: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    include_tax_type: {
      type: 'integer',
      enum: INCLUDE_TAX_TYPE_OPTIONS.map(item => item.value),
    },
    receipt_number: {
      type: 'integer',
      enum: Object.values(RECEIPT_NUMBER_VALUE_ENUM),
    },
  },
  if: {
    properties: {
      enable_session_timeout: { const: true },
    },
  },
  then: {
    required: ['session_expirytime'],
  },
};

export default updateSettingSchema;
